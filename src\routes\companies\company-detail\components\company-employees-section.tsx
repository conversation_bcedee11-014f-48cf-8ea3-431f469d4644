import { Avatar, Container, Heading, Table, Text } from "@medusajs/ui"
import { useTranslation } from "react-i18next"
import { Company } from "../../../../types"

interface CompanyEmployeesSectionProps {
  company: Company
}

export const CompanyEmployeesSection = ({ company }: CompanyEmployeesSectionProps) => {
  const { t } = useTranslation()

  if (!company.employees || company.employees.length === 0) {
    return (
      <Container className="p-6">
        <Heading level="h2" className="mb-4">
          {t("companies.employees.title", "Employees")}
        </Heading>
        <Text className="text-ui-fg-subtle">
          {t("companies.employees.noEmployees", "No employees found for this company")}
        </Text>
      </Container>
    )
  }

  return (
    <Container className="p-0">
      <div className="px-6 py-4 border-b">
        <Heading level="h2">
          {t("companies.employees.title", "Employees")}
        </Heading>
      </div>

      <Table>
        <Table.Header>
          <Table.Row>
            <Table.HeaderCell></Table.HeaderCell>
            <Table.HeaderCell>
              {t("companies.employees.name", "Name")}
            </Table.HeaderCell>
            <Table.HeaderCell>
              {t("companies.employees.email", "Email")}
            </Table.HeaderCell>
            <Table.HeaderCell>
              {t("companies.employees.phone", "Phone")}
            </Table.HeaderCell>
            <Table.HeaderCell>
              {t("companies.employees.role", "Role")}
            </Table.HeaderCell>
            <Table.HeaderCell>
              {t("companies.employees.spendingLimit", "Spending Limit")}
            </Table.HeaderCell>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {company.employees.map((employee) => (
            <Table.Row key={employee.id}>
              <Table.Cell className="w-6 h-6 items-center justify-center">
                <Avatar
                  src={undefined}
                  fallback={employee.customer.first_name?.charAt(0) || "U"}
                  className="h-8 w-8"
                />
              </Table.Cell>
              <Table.Cell>
                <div>
                  <Text className="font-medium">
                    {employee.customer.first_name} {employee.customer.last_name}
                  </Text>
                </div>
              </Table.Cell>
              <Table.Cell>
                <Text>{employee.customer.email}</Text>
              </Table.Cell>
              <Table.Cell>
                <Text>{employee.customer.phone || "-"}</Text>
              </Table.Cell>
              <Table.Cell>
                <Text>
                  {employee.is_admin 
                    ? t("companies.employees.admin", "Admin")
                    : t("companies.employees.employee", "Employee")
                  }
                </Text>
              </Table.Cell>
              <Table.Cell>
                <Text>
                  {employee.spending_limit 
                    ? new Intl.NumberFormat("en-US", {
                        style: "currency",
                        currency: "USD", // TODO: 使用公司货币
                      }).format(employee.spending_limit / 100)
                    : "-"
                  }
                </Text>
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
    </Container>
  )
}