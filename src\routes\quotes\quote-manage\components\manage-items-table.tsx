import { AdminOrderLineItem } from "@medusajs/framework/types";
import { DataTable } from "@medusajs/ui";
import { createColumnHelper } from "@tanstack/react-table";
import { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useDataTable } from "../../../../hooks/use-data-table";

type ManageItemsTableProps = {
  items: AdminOrderLineItem[];
  onSelectionChange?: (selectedItems: AdminOrderLineItem[]) => void;
};

const columnHelper = createColumnHelper<AdminOrderLineItem>();

export const ManageItemsTable = ({
  items,
  onSelectionChange,
}: ManageItemsTableProps) => {
  const { t } = useTranslation();

  const columns = useMemo(
    () => [
      columnHelper.accessor("product_title", {
        header: t("fields.product"),
        cell: ({ getValue, row }) => (
          <div>
            <div className="font-medium">{getValue()}</div>
            <div className="text-sm text-ui-fg-subtle">
              {row.original.variant_title}
            </div>
          </div>
        ),
      }),
      columnHelper.accessor("quantity", {
        header: t("fields.quantity"),
        cell: ({ getValue }) => getValue(),
      }),
      columnHelper.accessor("unit_price", {
        header: t("fields.unitPrice"),
        cell: ({ getValue }) => {
          const price = getValue();
          return price ? `$${(price / 100).toFixed(2)}` : "-";
        },
      }),
      columnHelper.accessor("total", {
        header: t("fields.total"),
        cell: ({ row }) => {
          const quantity = row.original.quantity;
          const unitPrice = row.original.unit_price || 0;
          const total = quantity * unitPrice;
          return `$${(total / 100).toFixed(2)}`;
        },
      }),
    ],
    [t]
  );

  const { table } = useDataTable({
    data: items,
    columns,
    enableRowSelection: true,
    getRowId: (row) => row.id,
    onRowSelectionChange: (selection) => {
      if (onSelectionChange) {
        const selectedItems = items.filter((item) =>
          Object.keys(selection).includes(item.id)
        );
        onSelectionChange(selectedItems);
      }
    },
  });

  return (
    <div className="flex size-full flex-col overflow-hidden">
      <DataTable table={table} columns={columns} />
    </div>
  );
};