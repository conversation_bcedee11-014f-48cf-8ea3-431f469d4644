import { <PERSON><PERSON>, Badge, Container, Heading, Text } from "@medusajs/ui"
import { useTranslation } from "react-i18next"
import { Company } from "../../../../types"

interface CompanyGeneralSectionProps {
  company: Company
}

export const CompanyGeneralSection = ({ company }: CompanyGeneralSectionProps) => {
  const { t } = useTranslation()

  return (
    <Container className="divide-y p-0">
      <div className="flex items-center gap-4 px-6 py-4">
        <Avatar
          src={company.logo_url || undefined}
          fallback={company.name.charAt(0)}
          className="h-16 w-16"
        />
        <div>
          <Heading level="h2">{company.name}</Heading>
          <Text className="text-ui-fg-subtle">{company.email}</Text>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 px-6 py-4">
        <div>
          <Text className="text-ui-fg-subtle text-sm mb-1">
            {t("companies.fields.phone", "Phone")}
          </Text>
          <Text className="font-medium">
            {company.phone || "-"}
          </Text>
        </div>

        <div>
          <Text className="text-ui-fg-subtle text-sm mb-1">
            {t("companies.fields.website", "Website")}
          </Text>
          <Text className="font-medium">
            {company.website || "-"}
          </Text>
        </div>

        <div className="col-span-2">
          <Text className="text-ui-fg-subtle text-sm mb-1">
            {t("companies.fields.address", "Address")}
          </Text>
          <Text className="font-medium">
            {`${company.address}, ${company.city}, ${company.state} ${company.zip}`}
          </Text>
        </div>

        <div>
          <Text className="text-ui-fg-subtle text-sm mb-1">
            {t("companies.fields.employees", "Employees")}
          </Text>
          <Text className="font-medium">
            {company.employees?.length || 0}
          </Text>
        </div>

        <div>
          <Text className="text-ui-fg-subtle text-sm mb-1">
            {t("companies.fields.customerGroup", "Customer Group")}
          </Text>
          {company.customer_group?.name ? (
            <Badge size="small" color="blue">
              {company.customer_group.name}
            </Badge>
          ) : (
            <Text>-</Text>
          )}
        </div>

        <div className="col-span-2">
          <Text className="text-ui-fg-subtle text-sm mb-1">
            {t("companies.fields.description", "Description")}
          </Text>
          <Text className="font-medium">
            {company.description || "-"}
          </Text>
        </div>
      </div>

      <div className="px-6 py-4">
        <div className="flex justify-between items-center text-sm">
          <Text className="text-ui-fg-subtle">
            {t("general.createdAt", "Created")}
          </Text>
          <Text>
            {new Date(company.created_at).toLocaleDateString()}
          </Text>
        </div>
      </div>
    </Container>
  )
}