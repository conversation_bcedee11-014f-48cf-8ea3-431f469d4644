import { FetchError } from "@medusajs/js-sdk"
import {
  QueryKey,
  UseMutationOptions,
  UseQueryOptions,
  useMutation,
  useQuery,
} from "@tanstack/react-query"
import { sdk } from "../../lib/client"
import { queryClient } from "../../lib/query-client"
import {
  Company,
  AdminCompanyResponse,
  AdminCompaniesResponse,
  AdminCreateCompany,
  AdminUpdateCompany,
} from "../../types"
import { companiesQueryKeys } from "./query-keys"

// 基础查询hooks
export const useCompanies = (
  query?: Record<string, any>,
  options?: Omit<
    UseQueryOptions<
      AdminCompaniesResponse,
      FetchError,
      AdminCompaniesResponse,
      QueryKey
    >,
    "queryFn" | "queryKey"
  >
) => {
  const { data, ...rest } = useQuery({
    queryKey: companiesQueryKeys.list(query),
    queryFn: async () => {
      return sdk.client.fetch<AdminCompaniesResponse>(`/admin/companies`, {
        query,
      })
    },
    ...options,
  })

  return { companies: data?.companies, count: data?.count, ...rest }
}

export const useCompany = (
  companyId: string,
  query?: Record<string, any>,
  options?: Omit<
    UseQueryOptions<
      AdminCompanyResponse,
      FetchError,
      AdminCompanyResponse,
      QueryKey
    >,
    "queryFn" | "queryKey"
  >
) => {
  const { data, ...rest } = useQuery({
    queryKey: companiesQueryKeys.detail(companyId, query),
    queryFn: async () => {
      return sdk.client.fetch<AdminCompanyResponse>(`/admin/companies/${companyId}`, {
        query,
      })
    },
    ...options,
  })

  return { company: data?.company, ...rest }
}

// 变更操作hooks
export const useCreateCompany = (
  options?: UseMutationOptions<
    AdminCompanyResponse,
    FetchError,
    AdminCreateCompany
  >
) => {
  return useMutation({
    mutationFn: async (payload: AdminCreateCompany) => {
      return sdk.client.fetch<AdminCompanyResponse>("/admin/companies", {
        method: "POST",
        body: payload,
      })
    },
    onSuccess: (data, variables, context) => {
      queryClient.invalidateQueries({ queryKey: companiesQueryKeys.lists() })
      if (data.company?.id) {
        queryClient.invalidateQueries({ 
          queryKey: companiesQueryKeys.detail(data.company.id) 
        })
      }
      options?.onSuccess?.(data, variables, context)
    },
    ...options,
  })
}

export const useUpdateCompany = (
  companyId: string,
  options?: UseMutationOptions<
    AdminCompanyResponse,
    FetchError,
    AdminUpdateCompany
  >
) => {
  return useMutation({
    mutationFn: async (payload: AdminUpdateCompany) => {
      return sdk.client.fetch<AdminCompanyResponse>(`/admin/companies/${companyId}`, {
        method: "POST",
        body: payload,
      })
    },
    onSuccess: (data, variables, context) => {
      queryClient.invalidateQueries({ queryKey: companiesQueryKeys.lists() })
      queryClient.invalidateQueries({ queryKey: companiesQueryKeys.detail(companyId) })
      options?.onSuccess?.(data, variables, context)
    },
    ...options,
  })
}

export const useDeleteCompany = (
  companyId: string,
  options?: UseMutationOptions<void, FetchError, void>
) => {
  return useMutation({
    mutationFn: async () => {
      return sdk.client.fetch<void>(`/admin/companies/${companyId}`, {
        method: "DELETE",
      })
    },
    onSuccess: (data, variables, context) => {
      queryClient.invalidateQueries({ queryKey: companiesQueryKeys.lists() })
      queryClient.invalidateQueries({ queryKey: companiesQueryKeys.detail(companyId) })
      options?.onSuccess?.(data, variables, context)
    },
    ...options,
  })
}// 客户组
管理hooks
export const useAddCompanyToCustomerGroup = (
  companyId: string,
  options?: UseMutationOptions<void, FetchError, string>
) => {
  return useMutation({
    mutationFn: async (groupId: string) => {
      return sdk.client.fetch(`/admin/companies/${companyId}/customer-group`, {
        method: "POST",
        body: { group_id: groupId },
      })
    },
    onSuccess: (data, variables, context) => {
      queryClient.invalidateQueries({ queryKey: companiesQueryKeys.lists() })
      queryClient.invalidateQueries({ queryKey: companiesQueryKeys.detail(companyId) })
      options?.onSuccess?.(data, variables, context)
    },
    ...options,
  })
}

export const useRemoveCompanyFromCustomerGroup = (
  companyId: string,
  options?: UseMutationOptions<void, FetchError, string>
) => {
  return useMutation({
    mutationFn: async (groupId: string) => {
      return sdk.client.fetch(
        `/admin/companies/${companyId}/customer-group/${groupId}`,
        {
          method: "DELETE",
        }
      )
    },
    onSuccess: (data, variables, context) => {
      queryClient.invalidateQueries({ queryKey: companiesQueryKeys.lists() })
      queryClient.invalidateQueries({ queryKey: companiesQueryKeys.detail(companyId) })
      options?.onSuccess?.(data, variables, context)
    },
    ...options,
  })
}